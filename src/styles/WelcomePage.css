/* WelcomePage.css - 欢迎页面样式 */
@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Titillium+Web:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* Features Section */
.features-section {
  position: relative;
  min-height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  background: linear-gradient(180deg, #010314 0%, #0b0c14 50%, #0d1024 100%);
  overflow: hidden;
  z-index: 20;
  scroll-snap-align: start;
  border-top: 1px solid rgba(77, 200, 255, 0.1);
  border-bottom: 1px solid rgba(77, 200, 255, 0.1);
}

/* 背景渐变效果优化 */
.features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 30%, rgba(77, 200, 255, 0.08), transparent 70%);
  z-index: -2;
  pointer-events: none;
}

.features-container {
  max-width: 1400px; /* Increased from 1200px */
  width: 100%;
  margin: 0 auto;
  padding: 0 60px; /* Increased from 40px */
  position: relative;
}

/* 特性标题部分 - 增强科技感 */
.features-header {
  margin-bottom: 50px;
  text-align: center;
  position: relative;
  background: transparent;
}

/* 技术风格标题容器 */
.tech-title-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 25px;
  overflow: hidden;
}

/* 标题装饰元素 */
.tech-title-decoration {
  display: flex;
  align-items: center;
  width: 220px;
  margin: 12px 0;
  position: relative;
}

.tech-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(0, 123, 255, 0.1),
    rgba(0, 223, 255, 0.7),
    rgba(0, 123, 255, 0.1));
  position: relative;
  overflow: hidden;
}

.tech-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(0, 240, 255, 0.8),
    transparent);
  animation: techLineScan 3s ease-in-out infinite;
}

@keyframes techLineScan {
  0% { left: -100%; }
  100% { left: 200%; }
}

.tech-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00eaff;
  margin: 0 15px;
  position: relative;
  box-shadow: 0 0 10px rgba(0, 234, 255, 0.7);
}

.tech-dot::after {
  content: '';
  position: absolute;
  top: -6px;
  left: -6px;
  right: -6px;
  bottom: -6px;
  border: 1px solid rgba(0, 234, 255, 0.3);
  border-radius: 50%;
  animation: techDotPulse 2s ease-in-out infinite;
}

@keyframes techDotPulse {
  0% { transform: scale(1); opacity: 0.3; }
  50% { transform: scale(1.5); opacity: 0.1; }
  100% { transform: scale(1); opacity: 0.3; }
}

/* 增强科技感的标题 */
.tech-enhanced-title {
  font-family: 'Orbitron', sans-serif;
  font-size: 3rem;
  font-weight: 600;
  letter-spacing: 3px;
  margin: 10px 0;
  padding: 0;
  color: #e0f7ff;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  text-shadow: 0 0 15px rgba(77, 200, 255, 0.6);
}

.tech-title-prefix {
  color: #4dc8ff;
  font-size: 2rem;
  margin-right: 15px;
  opacity: 0.8;
  text-shadow: 0 0 10px rgba(77, 200, 255, 0.6);
  position: relative;
  top: -2px;
}

.tech-title-letters {
  display: flex;
  position: relative;
}

.tech-letter {
  display: inline-block;
  color: #e0f7ff;
  letter-spacing: 3px;
  text-shadow: 0 0 15px rgba(0, 162, 255, 0.5);
}

/* 数字装饰元素 - 增强版 */
.tech-digital-decoration {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: -1;
  opacity: 0.7;
}

/* 增强二进制流的效果 */
.tech-binary-stream {
  position: absolute;
  left: 5%;
  top: 20%;
  opacity: 0.7;
  transform: rotate(-30deg) scale(0.9);
  animation: binaryFade 8s infinite alternate;
  z-index: 1;
  filter: drop-shadow(0 0 5px rgba(0, 234, 255, 0.4));
}

.tech-binary-stream.right {
  right: 5%;
  left: auto;
  top: 30%;
  transform: rotate(30deg) scale(0.9);
  animation-delay: 2s;
}

/* 添加更多二进制流 */
.tech-binary-stream.top-left {
  left: 15%;
  top: 10%;
  transform: rotate(-15deg) scale(0.7);
  animation-delay: 1s;
}

.tech-binary-stream.top-right {
  right: 15%;
  left: auto;
  top: 10%;
  transform: rotate(15deg) scale(0.7);
  animation-delay: 3s;
}

.tech-binary-stream.bottom-left {
  left: 10%;
  top: 70%;
  transform: rotate(-45deg) scale(0.7);
  animation-delay: 2.5s;
}

.tech-binary-stream.bottom-right {
  right: 10%;
  left: auto;
  top: 70%;
  transform: rotate(45deg) scale(0.7);
  animation-delay: 1.5s;
}

.binary-digit {
  display: inline-block;
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  color: #00eaff;
  margin: 0 1px;
  text-shadow: 0 0 10px rgba(0, 234, 255, 0.8);
  animation: binaryPulse 1.5s infinite;
}

.binary-digit:nth-child(odd) {
  animation-delay: 0.5s;
}

.binary-digit:nth-child(3n) {
  animation-delay: 1s;
}

/* 添加特殊突出的二进制数字 */
.binary-digit.highlight {
  font-size: 18px;
  color: #ffffff;
  text-shadow: 0 0 15px rgba(0, 234, 255, 1);
  animation: binaryHighlightPulse 2s infinite;
}

@keyframes binaryHighlightPulse {
  0%, 100% { opacity: 0.8; text-shadow: 0 0 15px rgba(0, 234, 255, 0.9); }
  50% { opacity: 1; text-shadow: 0 0 20px rgba(0, 234, 255, 1); }
}

@keyframes binaryPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes binaryFade {
  0% { opacity: 0.4; transform: translateY(0) rotate(-30deg) scale(0.9); filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.3)); }
  100% { opacity: 0.8; transform: translateY(-15px) rotate(-30deg) scale(0.9); filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.5)); }
}

.tech-binary-stream.right {
  animation-name: binaryFadeRight;
}

@keyframes binaryFadeRight {
  0% { opacity: 0.4; transform: translateY(0) rotate(30deg) scale(0.9); filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.3)); }
  100% { opacity: 0.8; transform: translateY(-15px) rotate(30deg) scale(0.9); filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.5)); }
}

/* 添加浮动数字元素 */
.floating-digits {
  position: absolute;
  color: #00eaff;
  font-family: 'Orbitron', monospace;
  text-shadow: 0 0 8px rgba(0, 234, 255, 0.7);
  opacity: 0.7;
  animation: floatingDigitAnimation 8s ease-in-out infinite;
  z-index: -1;
  filter: drop-shadow(0 0 5px rgba(0, 234, 255, 0.3));
}

@keyframes floatingDigitAnimation {
  0% { transform: translateY(0) rotate(0deg); opacity: 0.4; filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.2)); }
  50% { transform: translateY(-15px) rotate(5deg); opacity: 0.9; filter: drop-shadow(0 0 8px rgba(0, 234, 255, 0.4)); }
  100% { transform: translateY(0) rotate(0deg); opacity: 0.4; filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.2)); }
}

/* 技术感十足的副标题 */
.features-subtitle {
  font-family: 'Rajdhani', sans-serif;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 auto;
  max-width: 800px;
  letter-spacing: 0.5px;
  line-height: 1.6;
  position: relative;
  opacity: 0;
  animation: subtitleFadeIn 1s ease-out forwards 0.5s;
}

@keyframes subtitleFadeIn {
  0% { opacity: 0; transform: translateY(10px); }
  100% { opacity: 1; transform: translateY(0); }
}

/* 背景粒子效果 */
.features-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
  opacity: 0.6;
}

.feature-particle {
  position: absolute;
  background: rgba(74, 158, 255, 0.5);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(74, 158, 255, 0.4);
  animation: particleFloat linear infinite;
}

@keyframes particleFloat {
  0% { transform: translateY(0) translateX(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateY(-100vh) translateX(10px); opacity: 0; }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tech-enhanced-title {
    font-size: 2.2rem;
  }

  .tech-title-prefix {
    font-size: 1.5rem;
    margin-right: 10px;
  }

  .tech-title-decoration {
    width: 180px;
  }

  .features-subtitle {
    font-size: 1rem;
    padding: 0 20px;
  }
}

@media (max-width: 480px) {
  .tech-enhanced-title {
    font-size: 1.8rem;
  }

  .tech-title-prefix {
    font-size: 1.2rem;
    margin-right: 8px;
  }

  .tech-title-decoration {
    width: 140px;
  }

  .tech-dot {
    width: 6px;
    height: 6px;
    margin: 0 10px;
  }
}

/* Modern grid layout with 2 rows, 4 columns */
.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, auto);
  gap: 15px; /* 添加间隙替代边框 */
  margin: 0;
  position: relative;
  overflow: hidden;
  padding: 10px;
}

/* Clean feature card design */
.feature-card {
  position: relative;
  padding: 30px 25px;
  background: transparent;
  transition: all 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  height: 100%;
  margin: 0;
}

/* 清除以前的悬停效果 */

/* Card with border styling */
.card-with-border {
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 30, 60, 0.1);
  background: rgba(15, 25, 40, 0.2);
  backdrop-filter: blur(5px);
  border-radius: 12px;
  border: 1px solid rgba(77, 200, 255, 0.07);
  transition: all 0.3s ease;
  overflow: hidden;
  z-index: 1;
}

.card-with-border:hover {
  background: rgba(20, 30, 50, 0.3);
  border-color: rgba(77, 200, 255, 0.15);
  box-shadow: 0 8px 25px rgba(0, 80, 150, 0.2);
  transform: translateY(-3px);
}

/* 添加内部光晕效果 */
.card-with-border::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle at center, rgba(77, 200, 255, 0.08), transparent 60%);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.card-with-border:hover::after {
  opacity: 1;
}

/* 设置卡片内部内容样式 */
.card-with-border .feature-icon {
  margin-bottom: 25px;
}

.card-with-border h3 {
  font-weight: 600;
  margin-bottom: 12px;
}

.card-with-border p {
  opacity: 0.8;
  line-height: 1.6;
}

/* Icon styling */
.feature-icon {
  width: 50px;
  height: 50px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 50%;
  background: rgba(15, 25, 50, 0.3);
  border: 1px solid rgba(77, 200, 255, 0.1);
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 100, 255, 0.1);
}

.feature-card:hover .feature-icon {
  background: rgba(20, 40, 80, 0.4);
  border-color: rgba(77, 200, 255, 0.3);
  box-shadow: 0 0 20px rgba(0, 120, 255, 0.2);
  transform: translateY(-2px);
}

.feature-icon::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(77, 200, 255, 0.2), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
  pointer-events: none;
}

.feature-card:hover .feature-icon::after {
  opacity: 1;
}

.feature-icon svg {
  width: 24px;
  height: 24px;
  color: #4dc8ff;
  filter: drop-shadow(0 0 2px rgba(77, 200, 255, 0.5));
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.feature-card:hover .feature-icon svg {
  color: #7ddfff;
  filter: drop-shadow(0 0 5px rgba(77, 200, 255, 0.8));
  transform: scale(1.1);
}

/* Feature content layout */
.feature-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.feature-card h3 {
  color: #ffffff;
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-family: 'Titillium Web', sans-serif;
  font-weight: 600;
  text-align: left;
}

.feature-card p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.95rem;
  line-height: 1.5;
  text-align: left;
  margin: 0;
  font-family: 'Titillium Web', sans-serif;
}

/* Animation for feature cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  animation: fadeInUp 0.5s ease-out forwards;
  opacity: 0;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }
.feature-card:nth-child(7) { animation-delay: 0.7s; }
.feature-card:nth-child(8) { animation-delay: 0.8s; }

/* Responsive styles */
@media (max-width: 1200px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }
}

@media (max-width: 768px) {
  .features-section {
    padding: 50px 0;
  }

  .features-container {
    padding: 0 20px;
  }

  .feature-card {
    padding: 30px 20px;
  }

  .feature-card h3 {
    font-size: 1.1rem;
  }

  .feature-card p {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 5px;
  }

  .feature-card {
    flex-direction: row;
    align-items: center;
    padding: 25px 20px;
    margin: 0;
  }

  .card-with-border {
    border-radius: 6px;
  }

  .feature-icon {
    margin-bottom: 0;
    margin-right: 15px;
  }

  .feature-content {
    flex: 1;
  }
}

/* Animation for feature cards */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  animation: fadeInUp 0.6s ease-out forwards;
  opacity: 0;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }
.feature-card:nth-child(7) { animation-delay: 0.7s; }
.feature-card:nth-child(8) { animation-delay: 0.8s; }

/* 流星效果 */
.meteor-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh; /* 限制在视口高度内 */
  overflow: hidden;
  pointer-events: none;
  z-index: 1.5; /* 在星空之上，地球之下 */
}

.meteor {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  top: 10%;
  left: 50%;
  z-index: 10;
  transform-origin: top center;
}

.meteor::before {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.7) 30%, rgba(255, 255, 255, 0.3) 60%, transparent);
  transform: translateY(-1px) rotate(0deg);
  transform-origin: left center;
  animation: meteorTail 3s ease-out forwards;
  opacity: 0;
}

/* 流星头部 */
.meteor::after {
  content: '';
  position: absolute;
  width: 2px;
  height: 2px;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 50%;
  box-shadow: 0 0 2px 1px rgba(255, 255, 255, 0.8),
              0 0 4px 2px rgba(200, 255, 255, 0.6),
              0 0 6px 3px rgba(170, 220, 255, 0.4);
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  animation: meteorHead 3s ease-out forwards;
}

/* 基础流星动画 - 头部先消失，尾部渐变消失 */
@keyframes shootStarLeftArc {
  0% {
    opacity: 0;
    transform: translate(0, 0) rotate(-35deg);
  }
  5% {
    opacity: 1;
  }
  30% {
    opacity: 1;
  }
  70% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translate(-800px, 400px) rotate(-35deg);
  }
}

/* 流星尾部动画 - 实现头部先消失，尾部渐变消失的效果 */
@keyframes meteorTail {
  0%, 30% {
    width: 170px;
    opacity: 1;
  }
  70% {
    width: 170px;
    opacity: 1;
  }
  90% {
    width: 20px;
    opacity: 0.3;
  }
  100% {
    width: 0;
    opacity: 0;
  }
}

/* 流星头部动画 */
@keyframes meteorHead {
  0%, 70% {
    opacity: 1;
  }
  80% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes shootStarLeftArc2 {
  0% {
    opacity: 0;
    transform: translate(0, 0) rotate(-40deg);
  }
  5% {
    opacity: 1;
  }
  30% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translate(-900px, 350px) rotate(-40deg);
  }
}

/* 为每个流星配置不同的动画参数 */
.meteor-1 {
  animation: shootStarLeftArc 3s ease-out infinite;
  animation-delay: 0s;
}

.meteor-1::before {
  animation: meteorTail 3s ease-out infinite;
  animation-delay: 0s;
}

.meteor-1::after {
  animation: meteorHead 3s ease-out infinite;
  animation-delay: 0s;
}

.meteor-1::before {
  width: 170px;
  height: 2px;
}

.meteor-2 {
  top: 20%;
  left: 60%;
  animation: shootStarLeftArc2 3.5s ease-out infinite;
  animation-delay: 8s;
}

.meteor-2::before {
  animation: meteorTail 3.5s ease-out infinite;
  animation-delay: 8s;
}

.meteor-2::after {
  animation: meteorHead 3.5s ease-out infinite;
  animation-delay: 8s;
}

.meteor-2::before {
  width: 150px;
  height: 1.5px;
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.7) 20%, rgba(255, 255, 255, 0.2) 50%, transparent);
}

.meteor-3 {
  top: 15%;
  left: 40%;
  animation: shootStarLeftArc 4s ease-out infinite;
  animation-delay: 15s;
}

.meteor-3::before {
  animation: meteorTail 4s ease-out infinite;
  animation-delay: 15s;
}

.meteor-3::after {
  animation: meteorHead 4s ease-out infinite;
  animation-delay: 15s;
}

.meteor-3::before {
  width: 190px;
  height: 2.5px;
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.8) 20%, rgba(200, 230, 255, 0.4) 60%, transparent);
}

.meteor-3::after {
  width: 5px;
  height: 5px;
}

.meteor-4 {
  top: 10%;
  left: 55%;
  animation: shootStarLeftArc2 3.2s ease-out infinite;
  animation-delay: 25s;
}

.meteor-4::before {
  animation: meteorTail 3.2s ease-out infinite;
  animation-delay: 25s;
}

.meteor-4::after {
  animation: meteorHead 3.2s ease-out infinite;
  animation-delay: 25s;
}

.meteor-4::before {
  width: 140px;
  height: 1.8px;
  background: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0.7) 30%, rgba(200, 220, 255, 0.3) 70%, transparent);
}

.welcome-page {
  width: 100%;
  min-height: 100vh;
  background: linear-gradient(135deg, #010313 0%, #0a0d2c 30%, #0f1535 60%, #101842 100%);
  color: white;
  overflow-x: hidden;
  position: relative;
  scroll-behavior: smooth;
  scroll-snap-type: y mandatory;
}

.welcome-page::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 70vh; /* Limit the height of the gradient overlay */
  background: radial-gradient(ellipse at 50% 50%, rgba(60, 140, 255, 0.15) 0%, rgba(60, 140, 255, 0.05) 50%, transparent 100%);
  z-index: 1;
  pointer-events: none;
  opacity: 0.7;
  transition: opacity 0.5s ease;
}

.welcome-page:hover::before {
  opacity: 0.9;
}

.welcome-page::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18C11.6 18 12 18.4 12 19C12 19.6 11.6 20 11 20C10.4 20 10 19.6 10 19C10 18.4 10.4 18 11 18ZM13 22C13.6 22 14 22.4 14 23C14 23.6 13.6 24 13 24C12.4 24 12 23.6 12 23C12 22.4 12.4 22 13 22ZM20 34C20.6 34 21 34.4 21 35C21 35.6 20.6 36 20 36C19.4 36 19 35.6 19 35C19 34.4 19.4 34 20 34ZM28 43C28.6 43 29 43.4 29 44C29 44.6 28.6 45 28 45C27.4 45 27 44.6 27 44C27 43.4 27.4 43 28 43ZM44 29C44.6 29 45 29.4 45 30C45 30.6 44.6 31 44 31C43.4 31 43 30.6 43 30C43 29.4 43.4 29 44 29ZM55 12C55.6 12 56 12.4 56 13C56 13.6 55.6 14 55 14C54.4 14 54 13.6 54 13C54 12.4 54.4 12 55 12ZM64 35C64.6 35 65 35.4 65 36C65 36.6 64.6 37 64 37C63.4 37 63 36.6 63 36C63 35.4 63.4 35 64 35ZM79 45C79.6 45 80 45.4 80 46C80 46.6 79.6 47 79 47C78.4 47 78 46.6 78 46C78 45.4 78.4 45 79 45ZM88 62C88.6 62 89 62.4 89 63C89 63.6 88.6 64 88 64C87.4 64 87 63.6 87 63C87 62.4 87.4 62 88 62ZM74 71C74.6 71 75 71.4 75 72C75 72.6 74.6 73 74 73C73.4 73 73 72.6 73 72C73 71.4 73.4 71 74 71ZM60 83C60.6 83 61 83.4 61 84C61 84.6 60.6 85 60 85C59.4 85 59 84.6 59 84C59 83.4 59.4 83 60 83ZM44 76C44.6 76 45 76.4 45 77C45 77.6 44.6 78 44 78C43.4 78 43 77.6 43 77C43 76.4 43.4 76 44 76ZM29 82C29.6 82 30 82.4 30 83C30 83.6 29.6 84 29 84C28.4 84 28 83.6 28 83C28 82.4 28.4 82 29 82ZM16 71C16.6 71 17 71.4 17 72C17 72.6 16.6 73 16 73C15.4 73 15 72.6 15 72C15 71.4 15.4 71 16 71ZM5 60C5.6 60 6 60.4 6 61C6 61.6 5.6 62 5 62C4.4 62 4 61.6 4 61C4 60.4 4.4 60 5 60Z' fill='rgba(255, 255, 255, 0.02)' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E");
  z-index: 0;
  pointer-events: none;
}

/* 加载屏幕 */
.loading-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #030814, #0a1632);
  z-index: 1000;
  overflow: hidden;
}

/* Tech grid background */
.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 140, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 140, 255, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
  z-index: -1;
  animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
  0% { background-position: 0 0; }
  100% { background-position: 30px 30px; }
}

/* Advanced earth loader */
.earth-loader {
  width: 160px;
  height: 160px;
  border-radius: 50%;
  background: radial-gradient(circle at 40% 40%, #001e45 0%, #062a58 40%, #0052b8 100%);
  position: relative;
  margin-bottom: 30px;
  box-shadow:
    0 0 40px rgba(0, 162, 255, 0.6),
    0 0 80px rgba(0, 68, 128, 0.3);
  animation: float 6s ease-in-out infinite;
  overflow: hidden;
}

@keyframes float {
  0% { transform: translateY(-5px) rotate(0deg); }
  50% { transform: translateY(5px) rotate(2deg); }
  100% { transform: translateY(-5px) rotate(0deg); }
}

/* Scanning line effect */
.scan-line {
  position: absolute;
  top: 0;
  left: -10%;
  width: 120%;
  height: 6px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(0, 247, 255, 0.2) 20%,
    rgba(55, 220, 255, 0.6) 50%,
    rgba(0, 247, 255, 0.2) 80%,
    transparent 100%);
  filter: blur(1px);
  z-index: 2;
  animation: scan 2.5s cubic-bezier(0.36, 0.11, 0.89, 0.32) infinite;
}

@keyframes scan {
  0% { top: -10px; opacity: 0; }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { top: 170px; opacity: 0; }
}

/* Rings around earth */
.loader-rings {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border-radius: 50%;
  border: 2px solid transparent;
  border-top: 2px solid rgba(0, 223, 255, 0.5);
  border-right: 2px solid rgba(61, 184, 255, 0.3);
  animation: rotateRing 8s linear infinite;
}

.loader-rings:before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  border-radius: 50%;
  border: 1px solid transparent;
  border-left: 1px solid rgba(102, 204, 255, 0.7);
  border-bottom: 1px solid rgba(102, 204, 255, 0.4);
  animation: rotateRing 5s linear infinite reverse;
}

.loader-rings:after {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border-radius: 50%;
  border: 1px dashed rgba(255, 255, 255, 0.3);
  animation: rotateRing 12s linear infinite;
  opacity: 0.6;
}

@keyframes rotateRing {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* High-tech loading text */
.loading-text {
  font-family: 'Orbitron', sans-serif;
  font-size: 16px;
  letter-spacing: 4px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15px;
  position: relative;
  text-transform: uppercase;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-text-tech {
  display: inline-block;
  position: relative;
  background: linear-gradient(90deg, rgba(26, 173, 255, 0.7), rgba(100, 220, 255, 0.9), rgba(26, 173, 255, 0.7));
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  text-shadow: 0 0 15px rgba(0, 162, 255, 0.3);
  font-weight: 500;
  animation: textPulse 2s infinite;
  padding: 0 15px;
}

.loading-text-tech:after {
  content: '...';
  position: relative;
  display: inline-block;
  animation: dots 1.5s infinite steps(4);
  overflow: hidden;
  vertical-align: bottom;
  width: 0;
}

@keyframes dots {
  0% { width: 0; }
  100% { width: 20px; }
}

@keyframes textPulse {
  0% { opacity: 0.7; filter: blur(0.2px); }
  50% { opacity: 1; filter: blur(0); }
  100% { opacity: 0.7; filter: blur(0.2px); }
}

/* Progress bar */
.loading-progress {
  width: 240px;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  margin-bottom: 30px;
}

.progress-bar {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, #0077ff, #00c6ff, #0077ff);
  border-radius: 2px;
  box-shadow: 0 0 10px rgba(0, 162, 255, 0.7);
  animation: progressAnimate 3s ease-in-out infinite;
  background-size: 200% 100%;
}

@keyframes progressAnimate {
  0% { width: 0%; background-position: 100% 0; }
  50% { width: 70%; }
  70% { width: 70%; }
  100% { width: 100%; background-position: 0 0; }
}

/* Tech circles */
.tech-circles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
}

.tech-circle {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(0, 162, 255, 0.3);
  filter: blur(0.4px);
  opacity: 0;
  animation: circleAppear 8s infinite;
}

.circle-1 {
  width: 100px;
  height: 100px;
  top: 20%;
  left: 15%;
  animation-delay: 0.5s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: 25%;
  right: 18%;
  animation-delay: 1.5s;
}

.circle-3 {
  width: 80px;
  height: 80px;
  top: 30%;
  right: 25%;
  animation-delay: 1s;
}

.circle-4 {
  width: 120px;
  height: 120px;
  bottom: 30%;
  left: 22%;
  animation-delay: 2s;
}

.circle-5 {
  width: 60px;
  height: 60px;
  top: 15%;
  right: 30%;
  animation-delay: 0s;
}

.circle-6 {
  width: 200px;
  height: 200px;
  top: 40%;
  left: 40%;
  animation-delay: 2.5s;
}

@keyframes circleAppear {
  0% { transform: scale(0.5); opacity: 0; }
  20% { transform: scale(1); opacity: 0.6; }
  40% { transform: scale(1.1); opacity: 0.4; }
  60% { transform: scale(1); opacity: 0.7; }
  80% { transform: scale(1.2); opacity: 0.3; }
  100% { transform: scale(0.5); opacity: 0; }
}

/* 内容部分 */
.content-section {
  width: 100%;
  height: 100vh;
  padding: 0 40px;
  z-index: 20;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  scroll-snap-align: start;
}

.title-section {
  max-width: 1000px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.subtitle {
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  color: #a8aabc;
  font-weight: 300;
}

.main-title {
  font-size: 6rem;
  line-height: 1;
  margin: 0;
  padding: 0;
  letter-spacing: 5px;
  font-weight: 500;
  text-transform: uppercase;
  background: linear-gradient(to right, #ffffff, #a8aabc);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.divider {
  display: none;
}

.divider-line {
  width: 100%;
  max-width: 500px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.7), transparent);
  margin: 15px 0;
}

.data-content .divider-line {
  width: 70%;
  max-width: 300px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.9), rgba(100, 160, 255, 0.2));
  margin: 15px 0 25px 0;
}

.description {
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 30px;
  color: #a8aabc;
  max-width: 80%;
}

/* 公司信息 */
.company-info {
  margin-top: 30px;
  margin-bottom: 30px;
  padding: 0;
  background: transparent;
  position: relative;
  overflow: visible;
}

.company-info::before {
  display: none;
}

.company-info::after {
  display: none;
}

.company-name {
  margin-top: 0;
  font-size: 5rem;
  margin-bottom: 35px;
  letter-spacing: 5px;
  font-weight: 600;
  color: #c4d8ff;
  text-shadow: 0 0 25px rgba(100, 160, 255, 0.8), 0 0 50px rgba(50, 130, 252, 0.5);
  font-family: 'Orbitron', 'Arial', sans-serif;
  text-transform: uppercase;
  position: relative;
  display: block;
  animation: titleGlow 4s ease-in-out infinite;
  line-height: 1.2;
  text-align: center;
  position: relative;
  z-index: 10;
}

.company-name::before {
  display: none;
}

.company-desc {
  margin: 0 auto 35px;
  color: #d0d4f1;
  line-height: 1.8;
  font-size: 1.15rem;
  letter-spacing: 0.6px;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  font-weight: 300;
  position: relative;
  z-index: 5;
  opacity: 0;
  animation: fadeIn 1s ease-in forwards 0.5s;
  max-height: 45vh;
  overflow-y: auto;
  padding: 0 50px;
  text-align: center;
  max-width: 900px;
}

@keyframes titleGlow {
  0% {
    text-shadow: 0 0 25px rgba(100, 160, 255, 0.8), 0 0 50px rgba(50, 130, 252, 0.5);
    color: #c4d8ff;
  }
  50% {
    text-shadow: 0 0 30px rgba(100, 160, 255, 1), 0 0 60px rgba(50, 130, 252, 0.8), 0 0 80px rgba(50, 130, 252, 0.4);
    color: #e0edff;
  }
  100% {
    text-shadow: 0 0 25px rgba(100, 160, 255, 0.8), 0 0 50px rgba(50, 130, 252, 0.5);
    color: #c4d8ff;
  }
}

/* 添加科技效果的装饰元素 */
.tech-decor {
  position: absolute;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(50, 130, 252, 0.2) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

.tech-decor-1 {
  width: 400px;
  height: 400px;
  top: 20%;
  left: 10%;
  opacity: 0.5;
}

.tech-decor-2 {
  width: 300px;
  height: 300px;
  bottom: 10%;
  right: 15%;
  animation-delay: 2s;
  opacity: 0.3;
}

.button-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  gap: 20px;
  flex-wrap: wrap;
}

/* 高亮按钮样式，用于特殊强调的按钮 */
.data-cta-button .starry-button,
.explore-platform-btn {
  font-size: 1.05rem;
  padding: 14px 36px;
  border-width: 2px;
  animation: buttonGlow 3s infinite;
}

.data-cta-button .starry-button:hover,
.explore-platform-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(100, 200, 255, 0.5);
  border-color: rgba(100, 200, 255, 0.8);
}

/* Star particles */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
  overflow: hidden;
}

.particle {
  position: absolute;
  background-color: #fff;
  border-radius: 50%;
  pointer-events: none;
  animation: float var(--duration, 3s) ease-in-out var(--delay, 0s) infinite;
  opacity: 0;
  box-shadow: 0 0 5px 1px rgba(255, 255, 255, 0.8);
  width: var(--size, 2px);
  height: var(--size, 2px);
  left: var(--x, 50%);
  top: var(--y, 50%);
  transform: translate(-50%, -50%);
}

/* Button hover and active states */
.explore-platform-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(100, 200, 255, 0.5);
  border-color: rgba(100, 200, 255, 0.8);
}

.explore-platform-btn:hover .btn-text {
  text-shadow: 0 0 15px rgba(100, 200, 255, 0.8);
}

.explore-platform-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(0, 100, 255, 0.4),
              inset 0 0 10px rgba(100, 200, 255, 0.3);
}

/* Animation for floating particles */
@keyframes float {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0;
  }
  20%, 80% {
    opacity: 0.8;
  }
  50% {
    transform: translate(
      calc(-50% + (var(--i) - 0.5) * 40px),
      calc(-50% - 30px)
    ) scale(1.2);
    opacity: 1;
  }
}

/* Glow effect */
.explore-platform-btn::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  transform: rotate(0deg);
  animation: rotateGlow 8s linear infinite;
  z-index: 0;
  pointer-events: none;
}

@keyframes rotateGlow {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .explore-platform-btn {
    padding: 12px 24px;
    font-size: 14px;
  }

  .particle {
    --size: 1.5px;
  }
}

.scroll-down-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.scroll-down-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #ffffff;
  background-color: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 50px;
  font-size: 0.9rem;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.scroll-down-btn svg {
  transition: transform 0.3s ease;
}

.scroll-down-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
}

.scroll-down-btn:hover svg {
  transform: translateY(3px);
}

.earth-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: 10; /* 提高地球容器的z-index */
  pointer-events: none;
  overflow: hidden; /* 确保内部内容不会溢出 */
}

.earth-container canvas {
  display: block;
  width: 100%;
  height: 100%;
}

/* 团队部分背景样式 */
.team-section {
  min-height: 100vh;
  width: 100%;
  position: relative;
  z-index: 20;
  scroll-snap-align: start;
  background: #0b0c14;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  overflow: hidden;
  position: relative;
}

/* 添加网格背景 */
.team-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(to right, rgba(140, 123, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(140, 123, 255, 0.05) 1px, transparent 1px);
  background-size: 50px 50px;
  opacity: 0.3;
  pointer-events: none;
  z-index: -1;
}

.team-section-inner {
  max-width: 1600px;
  width: 100%;
  padding: 0 80px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  z-index: 2;
}

.team-left-content {
  flex: 0 0 35%;
  padding-right: 0;
  margin-top: 0;
  position: relative;
  z-index: 10;
  transform: translateY(-40px);
}

.team-carousel {
  flex: 0 0 65%;
  position: relative;
  padding-top: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-left: -15%;
  z-index: 5;
  padding-bottom: 60px; /* Add space for navigation buttons */
}

.team-layout {
  display: flex;
  flex-direction: row;
  width: 100%;
  position: relative;
  gap: 0;
  align-items: flex-start;
}

.team-info-area {
  position: relative;
  left: auto;
  top: auto;
  transform: none;
  width: 35%;
  z-index: 5;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
  padding-top: 60px;
}

.team-photos-area {
  position: relative;
  width: 65%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: visible;
}

.photos-container {
  width: 100%;
  height: 550px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  perspective: 1000px;
  overflow: visible;
}

/* 添加过渡效果样式 */
.photos-container.transitioning .team-photo-card {
  transition: transform 0.4s ease-out, opacity 0.4s ease-out, filter 0.4s ease-out;
}

/* 向左过渡 - 用于"下一张"按钮点击（照片向左移动，显示右侧的下一张） */
.photos-container.transitioning.right .team-photo-card.active {
  transform: translateX(calc(-100% - 60px)) scale(0.75);
  opacity: 0.3;
  filter: brightness(0.3) blur(1px);
}

.photos-container.transitioning.right .team-photo-card.next {
  transform: translateX(0) scale(1);
  opacity: 1;
  filter: brightness(1) blur(0);
  z-index: 5;
}

.photos-container.transitioning.right .team-photo-card.far-next {
  transform: translateX(calc(100% + 60px)) scale(0.75);
  opacity: 0.3;
}

.photos-container.transitioning.right .team-photo-card.prev {
  transform: translateX(calc(-200% - 100px)) scale(0.6);
  opacity: 0.2;
}

.photos-container.transitioning.right .team-photo-card.far-prev {
  transform: translateX(calc(-300% - 140px)) scale(0.5);
  opacity: 0;
}

/* 向右过渡 - 用于"上一张"按钮点击（照片向右移动，显示左侧的上一张） */
.photos-container.transitioning.left .team-photo-card.active {
  transform: translateX(calc(100% + 60px)) scale(0.75);
  opacity: 0.3;
  filter: brightness(0.3) blur(1px);
}

.photos-container.transitioning.left .team-photo-card.prev {
  transform: translateX(0) scale(1);
  opacity: 1;
  filter: brightness(1) blur(0);
  z-index: 5;
}

.photos-container.transitioning.left .team-photo-card.next {
  transform: translateX(calc(200% + 100px)) scale(0.6);
  opacity: 0.2;
}

.photos-container.transitioning.left .team-photo-card.far-prev {
  transform: translateX(calc(-100% - 60px)) scale(0.75);
  opacity: 0.3;
}

.photos-container.transitioning.left .team-photo-card.far-next {
  transform: translateX(calc(300% + 140px)) scale(0.5);
  opacity: 0;
}

.team-photo-card {
  position: absolute;
  width: 400px;
  height: 550px;
  border-radius: 32px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease, opacity 0.3s ease, filter 0.3s ease; /* 更精确的过渡属性和更快的时间 */
  transform-origin: center center;
  will-change: transform, opacity, filter;
  backface-visibility: hidden;
}

.team-photo-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* 中间活跃照片 */
.team-photo-card.active {
  z-index: 5;
  opacity: 1;
  transform: translateX(0) scale(1);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
  transition-duration: 0.25s; /* 更快的过渡 */
}

/* 右侧第一张照片 */
.team-photo-card.next {
  z-index: 4;
  opacity: 0.3;
  transform: translateX(calc(100% + 60px)) scale(0.75);
  filter: brightness(0.3) blur(1px);
  transition-duration: 0.25s; /* 更快的过渡 */
}

/* 右侧第二张照片 */
.team-photo-card.far-next {
  z-index: 3;
  opacity: 0.2;
  transform: translateX(calc(200% + 100px)) scale(0.6);
  filter: brightness(0.2) blur(2px);
  transition-duration: 0.25s; /* 更快的过渡 */
}

/* 左侧第一张照片 */
.team-photo-card.prev {
  z-index: 4;
  opacity: 0.3;
  transform: translateX(calc(-100% - 60px)) scale(0.75);
  filter: brightness(0.3) blur(1px);
  transition-duration: 0.25s; /* 更快的过渡 */
}

/* 左侧第二张照片 */
.team-photo-card.far-prev {
  z-index: 3;
  opacity: 0.2;
  transform: translateX(calc(-200% - 100px)) scale(0.6);
  filter: brightness(0.2) blur(2px);
  transition-duration: 0.25s; /* 更快的过渡 */
}

.team-navigation {
  position: absolute;
  display: flex;
  gap: 16px;
  bottom: -60px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  justify-content: center;
  width: 100%;
  padding-top: 20px;
}

.member-info-content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 0;
  animation: fadeIn 0.6s ease forwards;
  will-change: opacity, transform;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px 30px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85), rgba(0, 0, 0, 0.5) 70%, transparent);
  border-radius: 0 0 32px 32px;
}

.member-info-content .member-role,
.member-info-content .member-name,
.member-info-content .member-social {
  opacity: 0;
  animation: slideUp 0.5s ease forwards;
}

.member-info-content .member-role {
  font-family: 'Rajdhani', sans-serif;
  font-size: 16px;
  color: #8c7bff; /* 紫色调 */
  margin-bottom: 5px;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-weight: 500;
  animation-delay: 0.3s;
}

.member-info-content .member-name {
  font-family: 'Rajdhani', sans-serif;
  font-size: 42px; /* 更适合的字体大小 */
  color: #ffffff;
  margin: 0 0 15px 0;
  font-weight: 600;
  line-height: 1;
  animation-delay: 0.4s;
}

.member-info-content .member-social {
  display: flex;
  gap: 16px;
  margin-top: 10px;
  animation-delay: 0.5s;
}

.social-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.social-icon:hover {
  background: rgba(140, 123, 255, 0.3);
  transform: translateY(-3px);
}

.social-icon svg {
  width: 18px;
  height: 18px;
  transition: all 0.2s ease;
}

.social-icon:hover svg {
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 媒体查询调整 */
@media (max-width: 1600px) {
  .team-section-inner {
    padding: 0 60px;
  }

  .team-layout {
    gap: 50px;
  }

  .team-photo-card {
    width: 350px;
    height: 480px;
  }

  .member-info-content .member-name {
    font-size: 36px;
  }
}

@media (max-width: 1200px) {
  .team-section-inner {
    padding: 0 40px;
  }

  .team-left-content {
    flex: 0 0 30%;
  }

  .team-carousel {
    flex: 0 0 70%;
  }

  .team-photo-card {
    width: 320px;
    height: 440px;
  }

  .member-info-content .member-name {
    font-size: 32px;
  }
}

@media (max-width: 992px) {
  .team-section-inner {
    padding: 0 30px;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .team-left-content {
    flex: 0 0 auto;
    padding-right: 0;
    text-align: center;
    width: 100%;
    margin-top: 20px;
  }

  .team-carousel {
    flex: 0 0 auto;
    width: 100%;
    padding-top: 20px;
  }

  .team-layout {
    flex-direction: column-reverse;
    gap: 40px;
  }

  .team-info-area {
    width: 100%;
    margin-top: 40px;
    text-align: center;
    padding-right: 0;
    padding-top: 0;
  }

  .team-photos-area {
    width: 100%;
  }

  .photos-container {
    height: 440px;
  }

  .team-photo-card {
    width: 280px;
    height: 380px;
  }

  .team-navigation {
    justify-content: center;
  }

  .member-info-content {
    position: absolute;
    text-align: center;
    padding: 20px;
  }

  .member-info-content .member-social {
    justify-content: center;
  }

  .member-info-content .member-name {
    font-size: 28px;
    margin-bottom: 10px;
  }

  .member-info-content .member-role {
    margin-bottom: 5px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .team-section-inner {
    padding: 0 20px;
  }

  .photos-container {
    height: 380px;
  }

  .team-photo-card {
    width: 230px;
    height: 320px;
    border-radius: 24px;
  }

  /* 小屏幕时减少照片间距 */
  .team-photo-card.next {
    transform: translateX(calc(100% + 20px)) scale(0.7);
  }

  .team-photo-card.prev {
    transform: translateX(calc(-100% - 20px)) scale(0.7);
  }

  .team-photo-card.far-next {
    opacity: 0.2;
    transform: translateX(calc(200% + 40px)) scale(0.5);
  }

  .team-photo-card.far-prev {
    opacity: 0.2;
    transform: translateX(calc(-200% - 40px)) scale(0.5);
  }

  .member-info-content .member-name {
    font-size: 24px;
    margin-bottom: 5px;
  }

  .member-info-content .member-role {
    font-size: 12px;
    margin-bottom: 3px;
  }

  .social-icon {
    width: 28px;
    height: 28px;
  }

  .social-icon svg {
    width: 16px;
    height: 16px;
  }
}

@media screen and (max-width: 1200px) {
  .content-section {
    padding: 0 20px;
  }

  .earth-container {
    right: -400px;
  }

  .main-title {
    font-size: 4.5rem;
  }
}

@media screen and (max-width: 992px) {
  .welcome-page {
    flex-direction: column;
    height: auto;
  }

  .content-section {
    width: 100%;
    padding: 80px 20px;
    margin: 0;
    justify-content: center;
    text-align: center;
    min-height: 100vh;
  }

  .team-section-inner {
    flex-direction: column;
    gap: 30px;
  }

  .team-left-content {
    padding-right: 0;
    text-align: center;
  }

  .team-heading {
    text-align: center;
    max-width: 100%;
  }

  .team-right-content {
    width: 100%;
  }

  .team-member-photo {
    max-width: 300px;
    height: 400px;
  }

  .earth-container {
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    position: fixed;
  }

  .company-name {
    font-size: 4rem;
  }

  .company-desc {
    font-size: 1rem;
    padding: 0 20px;
  }

  .team-member-content {
    flex-direction: column-reverse;
    gap: 20px;
  }

  .member-info {
    width: 100%;
    padding: 20px;
    text-align: center;
  }

  .member-name {
    font-size: 36px;
  }

  .member-social {
    justify-content: center;
  }

  .member-photo-container {
    width: 100%;
    height: 350px;
  }

  .team-heading {
    text-align: center;
  }

  .team-title {
    font-size: 42px;
  }

  .team-subtitle {
    font-size: 20px;
  }
}

@media screen and (max-width: 576px) {
  .title-section {
    max-width: 100%;
  }

  .button-section {
    flex-direction: column;
  }

  .company-name {
    font-size: 3rem;
    margin-bottom: 20px;
  }

  .company-desc {
    font-size: 0.9rem;
    margin-bottom: 25px;
    padding: 0;
  }

  .scroll-down-container {
    margin-left: 0;
    margin-top: 15px;
  }

  .learn-more-btn {
    padding: 10px 25px;
    font-size: 14px;
  }

  .member-name {
    font-size: 28px;
  }

  .member-role {
    font-size: 12px;
  }

  .team-title {
    font-size: 32px;
  }

  .team-subtitle {
    font-size: 18px;
  }
}

@media screen and (max-width: 360px) {
  .company-name {
    font-size: 2.5rem;
  }

  .company-desc {
    font-size: 0.85rem;
    margin-bottom: 20px;
  }

  .learn-more-btn {
    padding: 8px 20px;
    font-size: 12px;
  }
}

.company-desc::-webkit-scrollbar {
  width: 5px;
}

.company-desc::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
}

.company-desc::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 5px;
}

.company-desc::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink {
  50% { border-color: transparent }
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0); }
}

@keyframes glow {
  0% { box-shadow: 0 0 15px rgba(50, 130, 252, 0.5); }
  50% { box-shadow: 0 0 30px rgba(50, 130, 252, 0.8); }
  100% { box-shadow: 0 0 15px rgba(50, 130, 252, 0.5); }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.team-member-slide.active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.animate-text {
  opacity: 0;
  animation: fadeInLeft 0.8s ease forwards;
}

.animate-fade-in {
  opacity: 0;
  animation: fadeIn 1s ease 0.5s forwards;
}

.team-member-slide.active .member-role {
  animation-delay: 0.2s;
}

.team-member-slide.active .member-name {
  animation-delay: 0.4s;
}

.highlight-text {
  position: relative;
  display: inline-block;
  color: #4d95ff;
  font-weight: 500;
  transition: color 0.3s ease;
  padding: 0 3px;
}

.highlight-text:hover {
  color: #77b0ff;
}

.highlight-text.wave {
  color: #64ffda;
}

.highlight-text.wave::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(to right, rgba(100, 255, 218, 0), rgba(100, 255, 218, 1), rgba(100, 255, 218, 0));
  animation: waveMove 2s infinite;
  transform-origin: bottom center;
  opacity: 0.7;
}

.highlight-text.circle {
  color: #ff7eb3;
}

.highlight-text.circle::before {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff7eb3;
  box-shadow: 0 0 10px rgba(255, 126, 179, 0.7);
  opacity: 0.6;
  transition: all 0.3s ease;
}

@keyframes waveMove {
  0% {
    transform: scaleX(1);
    opacity: 0.7;
  }
  50% {
    transform: scaleX(1.1);
    opacity: 1;
  }
  100% {
    transform: scaleX(1);
    opacity: 0.7;
  }
}

.highlight-text.circle:hover::before {
  transform: translateX(-50%) scale(1.5);
  opacity: 0.8;
  box-shadow: 0 0 15px rgba(255, 126, 179, 0.9);
}

/* Interactive analytics - Blue */
.highlight-text.diamond {
  color: #4dabf7;
  position: relative;
  z-index: 1;
  font-weight: 500;
  transition: all 0.3s ease;
}

.highlight-text.diamond::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #4dabf7;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.highlight-text.diamond:hover {
  color: #339af0;
  text-shadow: 0 0 10px rgba(77, 171, 247, 0.3);
}

.highlight-text.diamond:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* Customizable deployment - Purple */
.highlight-text.underline {
  color: #9c36b5;
  position: relative;
  z-index: 1;
  font-weight: 500;
  transition: all 0.3s ease;
}

.highlight-text.underline::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #9c36b5;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.highlight-text.underline:hover {
  color: #862e9c;
  text-shadow: 0 0 10px rgba(156, 54, 181, 0.3);
}

.highlight-text.underline:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

/* End-to-end decision support - Teal */
.highlight-text.highlight {
  color: #20c997;
  position: relative;
  z-index: 1;
  font-weight: 500;
  transition: all 0.3s ease;
}

.highlight-text.highlight::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #20c997;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.3s ease;
}

.highlight-text.highlight:hover {
  color: #12b886;
  text-shadow: 0 0 10px rgba(32, 201, 151, 0.3);
}

.highlight-text.highlight:hover::after {
  transform: scaleX(1);
  transform-origin: left;
}

@keyframes dataGlow {
  0% {
    text-shadow: 0 0 8px rgba(106, 90, 205, 0.4);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 20px rgba(147, 112, 219, 0.6),
                 0 0 30px rgba(186, 162, 224, 0.3);
    filter: brightness(1.1);
  }
}

@keyframes dataFlow {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes floatUp {
  0% {
    transform: translateY(0) scale(0.5);
    opacity: 0;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translateY(-20px) scale(1);
    opacity: 0;
  }
}

/* Keyframes for animations */
@keyframes holographic {
  0% {
    text-shadow: 0 0 8px rgba(0, 243, 255, 0.7);
    filter: brightness(1);
  }
  100% {
    text-shadow: 0 0 20px rgba(0, 243, 255, 0.9), 0 0 30px rgba(0, 255, 200, 0.6);
    filter: brightness(1.2);
  }
}

@keyframes digitalPulse {
  0%, 100% {
    opacity: 0.8;
    text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
  }
  50% {
    opacity: 1;
    text-shadow: 0 0 20px rgba(255, 0, 255, 0.8), 0 0 30px rgba(255, 0, 255, 0.5);
  }
}

@keyframes circuitPulse {
  0%, 100% {
    border-image-slice: 1;
    border-image-source: linear-gradient(90deg, #00ff88, #00ffea);
  }
  50% {
    border-image-source: linear-gradient(90deg, #00ffea, #00ff88);
  }
}

@keyframes circuitDot {
  0%, 100% {
    transform: scale(0.5);
    opacity: 0;
  }
  50% {
    transform: scale(1);
    opacity: 1;
  }
}

.highlight-text.wave:hover::after {
  animation-duration: 1s;
  opacity: 1;
}

/* 数据可视化部分 */
.data-section {
  min-height: 100vh;
  width: 100%;
  position: relative;
  z-index: 20;
  scroll-snap-align: start;
  background: rgba(0, 0, 0, 0.92);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  overflow: hidden;
}

.data-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.08'/%3E%3C/svg%3E");
  opacity: 0.25;
  pointer-events: none;
  display: block;
}

.data-section-inner {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 40px;
  position: relative;
  z-index: 2;
  background: radial-gradient(circle at 25% 50%, rgba(16, 20, 36, 0.3), transparent 60%);
}

.data-visualization-container {
  position: relative;
  width: 50%;
  height: 600px;
  z-index: 1;
  filter: drop-shadow(0 0 30px rgba(100, 160, 255, 0.3));
  overflow: visible;
}

.data-visualization-container canvas {
  border-radius: 50%;
  box-shadow: inset 0 0 50px rgba(100, 160, 255, 0.3);
}

/* 修改内容区域，增加与地球的视觉连接 */
.data-content {
  position: relative;
  z-index: 10;
  max-width: 50%;
  padding: 50px 60px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  background: linear-gradient(135deg, rgba(8, 11, 22, 0.7) 0%, rgba(16, 20, 36, 0.5) 100%);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 40px 0 rgba(88, 101, 242, 0.2);
  border: 1px solid rgba(100, 107, 255, 0.08);
  margin-left: -50px; /* 向左移动，与地球部分重叠 */
}

/* 添加闪光边框效果 */
.data-content::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  padding: 1px;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(88, 101, 242, 0.2) 25%,
    rgba(123, 90, 255, 0.4) 50%,
    rgba(88, 101, 242, 0.2) 75%,
    transparent 100%
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  animation: borderRotate 6s linear infinite;
}

@keyframes borderRotate {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 300% 0%;
  }
}

/* 添加背景微粒子 */
.data-content::after {
  content: '';
  position: absolute;
  inset: 0;
  background-image:
    radial-gradient(circle at 20% 30%, rgba(123, 90, 255, 0.1) 0%, transparent 8%),
    radial-gradient(circle at 80% 40%, rgba(88, 101, 242, 0.1) 0%, transparent 8%),
    radial-gradient(circle at 40% 70%, rgba(147, 112, 219, 0.1) 0%, transparent 8%),
    radial-gradient(circle at 70% 90%, rgba(100, 160, 255, 0.1) 0%, transparent 8%);
  opacity: 0.6;
  z-index: -1;
}

/* 添加闪光边框效果 */
.data-content::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 8px;
  padding: 1px;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(88, 101, 242, 0.2) 25%,
    rgba(123, 90, 255, 0.4) 50%,
    rgba(88, 101, 242, 0.2) 75%,
    transparent 100%
  );
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
  animation: borderRotate 6s linear infinite;
}

.data-content::after {
  content: '';
  position: absolute;
  left: -50%;
  top: -100%;
  width: 200%;
  height: 300%;
  background: linear-gradient(120deg,
    rgba(88, 101, 242, 0.05) 0%,
    rgba(101, 134, 255, 0.07) 25%,
    rgba(114, 137, 218, 0.08) 50%,
    rgba(126, 87, 194, 0.07) 75%,
    rgba(80, 72, 170, 0.05) 100%);
  filter: blur(40px);
  opacity: 0.6;
  z-index: 2;
  animation: flowLight 12s linear infinite;
  transform-origin: center center;
}

@keyframes flowLight {
  0% { transform: rotate(0deg) scale(1); opacity: 0.5;}
  50% { transform: rotate(180deg) scale(1.1); opacity: 0.7;}
  100% { transform: rotate(360deg) scale(1); opacity: 0.5;}
}

.data-title {
  font-family: 'Rajdhani', sans-serif;
  font-size: 3.4rem;
  font-weight: 300;
  letter-spacing: 0.08em;
  line-height: 1.2;
  text-transform: uppercase;
  margin-bottom: 18px;
  position: relative;
  z-index: 3;
  width: 100%;
  display: flex;
  flex-direction: column;
  filter: drop-shadow(0 0 15px rgba(160, 141, 255, 0.2));
}

.title-word {
  position: relative;
  display: inline-block;
  background: linear-gradient(90deg, #e0f7ff 0%, #c4a5ff 50%, #a2a8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
  margin-bottom: 0.1em;
  transform-origin: left;
  animation: wordAppear 0.8s cubic-bezier(0.215, 0.61, 0.355, 1) backwards;
}

.title-word:nth-child(1) { animation-delay: 0.1s; }
.title-word:nth-child(2) { animation-delay: 0.2s; }
.title-word:nth-child(3) { animation-delay: 0.3s; }
.title-word:nth-child(4) { animation-delay: 0.4s; }
.title-word:nth-child(5) { animation-delay: 0.5s; }
.title-word:nth-child(6) { animation-delay: 0.6s; }

.title-word::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #6246ea 0%, #a594f9 100%);
  animation: lineGrow 0.6s ease-out forwards;
  animation-delay: inherit;
  opacity: 0.7;
  filter: blur(0.5px);
}

@keyframes wordAppear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes lineGrow {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 100%;
    opacity: 0.7;
  }
}

.data-content .enhanced-divider {
  width: 120px;
  height: 1px;
  margin: 8px 0 25px 0;
  background: linear-gradient(90deg, rgba(182, 234, 255, 0.01) 0%, rgba(147, 112, 219, 0.8) 50%, rgba(182, 234, 255, 0.01) 100%);
  position: relative;
  z-index: 3;
  overflow: hidden;
}

.data-content .enhanced-divider::after {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0;
  width: 20%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0.9;
  filter: blur(1px);
  animation: dividerFlow 2s linear infinite;
}

@keyframes dividerFlow {
  0% { left: -20%; width: 20%;}
  100% { left: 100%; width: 20%;}
}

.data-subtitle-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 10px;
  z-index: 3;
  position: relative;
  width: 100%;
}

.data-subtitle.main-subtitle {
  font-family: 'Rajdhani', sans-serif;
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.25rem;
  font-weight: 400;
  letter-spacing: 0.02em;
  line-height: 1.6;
  position: relative;
  padding-left: 0;
  margin-bottom: 0;
  animation: subtitleFadeIn 1.2s cubic-bezier(.4,0,.2,1) both;
  text-shadow: 0 0 30px rgba(123, 90, 255, 0.6);
}

.data-subtitle.main-subtitle::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(to bottom, rgba(123, 90, 255, 0.1), rgba(123, 90, 255, 0.8), rgba(123, 90, 255, 0.1));
  border-radius: 1px;
}

.data-subtitle.secondary-subtitle {
  font-family: 'Rajdhani', sans-serif;
  color: rgba(220, 230, 255, 0.75);
  font-size: 1rem;
  font-weight: 300;
  letter-spacing: 0.015em;
  line-height: 1.6;
  position: relative;
  padding-left: 0;
  margin-top: 0;
  margin-bottom: 0;
  animation: subtitleFadeIn 1.6s cubic-bezier(.4,0,.2,1) 0.2s both;
}

.data-subtitle.secondary-subtitle::before {
  content: '';
  position: absolute;
  left: -20px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to bottom, rgba(182, 234, 255, 0.1), rgba(182, 234, 255, 0.4), rgba(182, 234, 255, 0.1));
  border-radius: 1px;
}

@keyframes subtitleFadeIn {
  0% { opacity: 0; transform: translateY(12px);}
  100% { opacity: 1; transform: translateY(0);}
}

/* 添加微动效 */
.data-content:hover .enhanced-divider::after {
  animation-duration: 1.5s;
}

.data-content:hover .data-title {
  animation-duration: 3s;
}

.request-access-btn {
  margin-top: 20px;
  background: linear-gradient(to right, #7b5aff, #6246ea);
  color: #ffffff;
  border: none;
  padding: 16px 36px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(123, 90, 255, 0.4), 0 0 30px rgba(123, 90, 255, 0.3);
  text-transform: uppercase;
  outline: none;
  position: relative;
  overflow: hidden;
  z-index: 1;
  align-self: flex-start;
}

.request-access-btn::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(90deg, #7b5aff, #6246ea, #7b5aff);
  z-index: -1;
  border-radius: 50px;
  background-size: 200%;
  animation: glowingBorder 3s linear infinite;
}

.request-access-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(123, 90, 255, 0.5);
  background: linear-gradient(to right, #8b6aff, #7256fb);
}

.request-access-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(123, 90, 255, 0.3);
}

@keyframes glowingBorder {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes floatElement {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-15px); }
  100% { transform: translateY(0px); }
}

/* 媒体查询适配 */
@media screen and (max-width: 992px) {
  .data-section-inner {
    flex-direction: column;
  }

  .data-visualization-container {
    width: 100%;
    height: 50vh;
    margin-bottom: 20px;
    min-height: 400px;
  }

  .data-content {
    max-width: 100%;
    text-align: center;
    align-items: center;
    padding: 30px;
  }

  .data-title {
    font-size: 3.2rem;
    text-align: center;
  }

  .data-subtitle {
    font-size: 1.6rem;
    margin-bottom: 40px;
    text-align: center;
    max-width: 100%;
  }

  .data-content .divider-line {
    margin: 15px auto 25px;
    background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.9), transparent);
  }

  .request-access-btn {
    align-self: center;
  }
}

@media screen and (max-width: 576px) {
  .data-title {
    font-size: 2.4rem;
  }

  .data-subtitle {
    font-size: 1.6rem;
    margin-bottom: 45px;
  }

  .data-content {
    padding: 20px;
  }

  .request-access-btn {
    padding: 14px 28px;
    font-size: 1rem;
  }
}

/* 申请访问模态框 */
.request-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(5, 10, 35, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease-in-out;
}

.request-modal {
  background: linear-gradient(135deg, rgba(25, 30, 60, 0.95) 0%, rgba(15, 20, 45, 0.95) 100%);
  border-radius: 20px;
  padding: 40px;
  width: 90%;
  max-width: 550px;
  position: relative;
  box-shadow: 0 0 50px rgba(100, 160, 255, 0.2);
  border: 1px solid rgba(100, 160, 255, 0.15);
  animation: scaleIn 0.3s ease-in-out;
  overflow: hidden;
}

.request-modal::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(to right, #7b5aff, #6246ea);
  z-index: 1;
}

.close-modal-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  background: transparent;
  border: none;
  color: #a8aabc;
  font-size: 24px;
  cursor: pointer;
  z-index: 10;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-modal-btn:hover {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
}

.modal-title {
  color: #ffffff;
  font-size: 2rem;
  margin-bottom: 10px;
  font-weight: 600;
  letter-spacing: 1px;
  text-align: center;
  font-family: 'Orbitron', 'Arial', sans-serif;
}

.modal-description {
  color: #a8aabc;
  text-align: center;
  margin-bottom: 30px;
  font-size: 1rem;
  line-height: 1.6;
}

.request-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  width: 100%;
}

.form-group {
  width: 100%;
}

.form-input {
  width: 100%;
  padding: 14px;
  border-radius: 10px;
  border: 1px solid rgba(100, 160, 255, 0.3);
  background-color: rgba(15, 20, 50, 0.5);
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
  outline: none;
}

.form-input::placeholder {
  color: #8a8daa;
}

.form-input:focus {
  border-color: rgba(123, 90, 255, 0.6);
  box-shadow: 0 0 0 2px rgba(123, 90, 255, 0.2);
}

.form-input:hover {
  border-color: rgba(123, 90, 255, 0.5);
}

.submit-request-btn {
  background: linear-gradient(to right, #7b5aff, #6246ea);
  color: #ffffff;
  border: none;
  padding: 16px 28px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(123, 90, 255, 0.4);
  text-transform: uppercase;
  outline: none;
  margin-top: 10px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.submit-request-btn.submitting {
  background: linear-gradient(to right, #6b4aef, #5236da);
  cursor: not-allowed;
  opacity: 0.8;
}

.submit-request-btn.submitting::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: loadingEffect 1.5s infinite;
}

.submit-request-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(123, 90, 255, 0.5);
  background: linear-gradient(to right, #8b6aff, #7256fb);
}

.submit-request-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(123, 90, 255, 0.3);
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* 媒体查询适配 */
@media screen and (max-width: 576px) {
  .request-modal {
    padding: 30px 20px;
  }

  .modal-title {
    font-size: 1.6rem;
  }

  .modal-description {
    font-size: 0.9rem;
    margin-bottom: 20px;
  }

  .form-input {
    padding: 12px;
  }

  .submit-request-btn {
    padding: 14px 20px;
  }
}

/* Starry Button Styles */
.starry-button {
  position: relative;
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 12px 30px;
  font-size: 1rem;
  font-family: 'Orbitron', sans-serif;
  letter-spacing: 2px;
  text-transform: uppercase;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  border-radius: 50px;
  backdrop-filter: blur(5px);
  z-index: 1;
}

.starry-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(100, 200, 255, 0.1), rgba(0, 100, 255, 0.1));
  z-index: -1;
  transition: all 0.3s ease;
  opacity: 0.5;
}

.starry-button:hover {
  border-color: rgba(100, 200, 255, 0.8);
  box-shadow: 0 0 15px rgba(100, 200, 255, 0.5);
  transform: translateY(-2px);
}

.starry-button:active {
  transform: translateY(0);
}

.starry-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(100, 200, 255, 0.3);
}

.starry-button-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.starry-button-text {
  position: relative;
  z-index: 1;
  text-shadow: 0 0 10px rgba(100, 200, 255, 0.5);
  transition: all 0.3s ease;
}

.starry-button:hover .starry-button-text {
  text-shadow: 0 0 15px rgba(100, 200, 255, 0.8);
}

/* Button glow effect */
@keyframes buttonGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(100, 200, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(100, 200, 255, 0.8);
  }
}

.starry-button.animate-glow {
  animation: buttonGlow 2s infinite;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .starry-button {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}

.form-success {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(to right, #64ffda, #34eea6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
  color: #ffffff;
  margin-bottom: 20px;
  box-shadow: 0 0 30px rgba(100, 255, 218, 0.5);
  animation: successPulse 2s infinite;
}

@keyframes loadingEffect {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes successPulse {
  0% {
    box-shadow: 0 0 20px rgba(100, 255, 218, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30px rgba(100, 255, 218, 0.7);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 20px rgba(100, 255, 218, 0.5);
    transform: scale(1);
  }
}

/* 导航按钮样式 */
.nav-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(140, 123, 255, 0.2);
  border: 1px solid rgba(140, 123, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(149, 128, 255, 0.7) 0%, rgba(140, 123, 255, 0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border-radius: 50%;
}

.nav-button:hover {
  background: rgba(140, 123, 255, 0.4);
  border-color: rgba(180, 163, 255, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(140, 123, 255, 0.5);
}

.nav-button:hover::before {
  opacity: 0.8;
  transform: scale(1.5);
  animation: pulseGlow 1.5s infinite alternate;
}

.nav-button:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
  box-shadow: 0 0 8px rgba(140, 123, 255, 0.3);
}

.nav-button svg {
  width: 20px;
  height: 20px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.nav-button:hover svg {
  transform: scale(1.1);
  stroke: #ffffff;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
  animation: pulseIcon 1s infinite alternate;
}

@keyframes pulseGlow {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    transform: scale(1.2);
  }
}

@keyframes pulseIcon {
  0% {
    transform: scale(1);
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
  }
  100% {
    transform: scale(1.15);
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.8));
  }
}

.team-heading {
  text-align: left;
  padding-top: 60px;
}

.team-title {
  font-size: 80px;
  font-weight: 700;
  margin-bottom: 10px;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 2px;
  line-height: 1.1;
  font-family: 'Rajdhani', sans-serif;
  background: linear-gradient(to right, #ffffff, #a8aabc);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  display: inline-block;
}

.team-title::after {
  content: '';
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(to right, #8c7bff, transparent);
}

.team-subtitle {
  font-size: 28px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 20px;
  letter-spacing: 1px;
  font-family: 'Rajdhani', sans-serif;
  position: relative;
}

/* 流光效果 */
.glow-effect {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.2;
  z-index: 1;
  pointer-events: none;
  transition: all 0.8s ease;
}

.glow-effect.top-left {
  top: -80px;
  left: -80px;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, rgba(126, 87, 194, 0.4) 0%, rgba(126, 87, 194, 0.1) 70%, transparent 100%);
  animation: pulseGlow 8s infinite alternate;
}

.glow-effect.bottom-right {
  bottom: -100px;
  right: -100px;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, rgba(88, 101, 242, 0.4) 0%, rgba(101, 134, 255, 0.1) 70%, transparent 100%);
  animation: pulseGlow 8s infinite alternate-reverse;
}

@keyframes pulseGlow {
  0% { transform: scale(1); opacity: 0.2; }
  50% { transform: scale(1.2); opacity: 0.3; }
  100% { transform: scale(1); opacity: 0.2; }
}

/* 微噪点层 */
.noise-layer {
  position: absolute;
  inset: 0;
  z-index: 2;
  pointer-events: none;
  opacity: 0.04;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.95' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.4'/%3E%3C/svg%3E");
  mix-blend-mode: overlay;
}

/* 极细动态边框 */
.dynamic-border {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 3;
  border: 1px solid rgba(147, 112, 219, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.dynamic-border::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.8) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: borderFlow 4s linear infinite;
}

@keyframes borderFlow {
  0% { left: -50%; top: 0; }
  25% { left: 100%; top: 0; }
  25.1% { left: 100%; top: 0; }
  25.2% { left: 100%; top: 0; }
  50% { left: 100%; top: 100%; }
  50.1% { top: 100%; left: 100%; }
  50.2% { top: 100%; left: 100%; }
  75% { left: -50%; top: 100%; }
  75.1% { left: -50%; top: 100%; }
  75.2% { left: -50%; top: 100%; }
  100% { left: -50%; top: 0; }
}

.data-title {
  font-family: 'Rajdhani', sans-serif;
  font-size: 3.4rem;
  font-weight: 300;
  letter-spacing: 0.08em;
  line-height: 1.2;
  text-transform: uppercase;
  margin-bottom: 18px;
  position: relative;
  z-index: 3;
  width: 100%;
  display: flex;
  flex-direction: column;
  filter: drop-shadow(0 0 15px rgba(160, 141, 255, 0.2));
}

.text-flow {
  color: rgba(255, 255, 255, 0.85);
  transition: all 0.3s ease;
}

.text-highlight {
  background: linear-gradient(90deg, #e0f7ff 0%, #c4a5ff 50%, #a2a8ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 0 15px rgba(160, 141, 255, 0.3));
  animation: textPulse 4s ease-in-out infinite;
  transition: all 0.3s ease;
}

@keyframes textPulse {
  0%, 100% { filter: drop-shadow(0 0 15px rgba(160, 141, 255, 0.3)); transform: translateY(0) scale(1);}
  50% { filter: drop-shadow(0 0 20px rgba(160, 141, 255, 0.5)); transform: translateY(-2px) scale(1.01);}
}

.data-content .enhanced-divider {
  width: 120px;
  height: 1px;
  margin: 8px 0 25px 0;
  background: linear-gradient(90deg, rgba(182, 234, 255, 0.01) 0%, rgba(147, 112, 219, 0.6) 50%, rgba(182, 234, 255, 0.01) 100%);
  position: relative;
  z-index: 3;
  overflow: hidden;
}

.divider-glow {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 20%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.01) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0.01) 100%);
  opacity: 0.9;
  filter: blur(1px);
  animation: dividerFlow 2s linear infinite;
}

/* 媒体查询 */
@media screen and (max-width: 992px) {
  .data-section-inner {
    flex-direction: column;
    padding: 30px 20px;
  }

  .data-visualization-container {
    width: 100%;
    height: 400px;
    margin-bottom: 30px;
  }

  .data-content {
    max-width: 100%;
    width: 100%;
    padding: 40px 30px;
    align-items: center;
    text-align: center;
  }

  .data-title {
    font-size: 2.5rem;
    text-align: center;
    align-items: center;
  }

  .data-content .enhanced-divider {
    margin-left: auto;
    margin-right: auto;
  }

  .data-subtitle-group {
    align-items: center;
  }

  .data-subtitle.main-subtitle,
  .data-subtitle.secondary-subtitle {
    text-align: center;
  }

  .data-subtitle.main-subtitle::before,
  .data-subtitle.secondary-subtitle::before {
    display: none;
  }
}

@media screen and (max-width: 576px) {
  .data-content {
    padding: 30px 20px;
  }

  .data-title {
    font-size: 1.8rem;
  }

  .data-subtitle.main-subtitle {
    font-size: 1rem;
  }

  .data-subtitle.secondary-subtitle {
    font-size: 0.9rem;
  }
}

/* 悬停交互效果 */
.data-content:hover .dynamic-border::after {
  animation-duration: 2.5s;
}

.data-content:hover .glow-effect.top-left {
  opacity: 0.3;
  transform: scale(1.1);
}

.data-content:hover .glow-effect.bottom-right {
  opacity: 0.3;
  transform: scale(1.1);
}

.data-content:hover .text-highlight {
  filter: drop-shadow(0 0 20px rgba(160, 141, 255, 0.5));
}

/* 添加连接线和粒子效果以使地球和文本更加融合 */
.globe-connector {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
  overflow: hidden;
}

.connector-line {
  position: absolute;
  background: linear-gradient(90deg, rgba(100, 160, 255, 0.1) 0%, rgba(147, 112, 219, 0.3) 50%, rgba(100, 160, 255, 0.1) 100%);
  height: 1px;
  transform-origin: left center;
  opacity: 0;
  filter: blur(1px);
  animation: lineAppear 2s ease-out forwards, lineGlow 4s infinite alternate;
}

@keyframes lineAppear {
  0% { width: 0; opacity: 0; }
  100% { width: 100%; opacity: 1; }
}

@keyframes lineGlow {
  0% { opacity: 0.2; }
  100% { opacity: 0.6; }
}

/* 添加发光边缘效果连接地球和内容 */
.globe-overlay-gradient {
  position: absolute;
  top: 0;
  right: -120px;
  width: 200px;
  height: 100%;
  background: linear-gradient(90deg,
    rgba(16, 20, 36, 0) 0%,
    rgba(88, 101, 242, 0.05) 20%,
    rgba(88, 101, 242, 0.1) 40%,
    rgba(88, 101, 242, 0.2) 60%,
    rgba(88, 101, 242, 0.3) 80%,
    rgba(16, 20, 36, 0.5) 100%
  );
  filter: blur(20px);
  z-index: 4;
  pointer-events: none;
  opacity: 0.8;
}

/* 给地球容器添加轻微的光晕效果 */
.data-visualization-container::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0;
  width: 150px;
  height: 150px;
  transform: translateY(-50%);
  background: radial-gradient(
    circle at right,
    rgba(100, 160, 255, 0.15),
    rgba(100, 160, 255, 0.05) 40%,
    transparent 70%
  );
  border-radius: 50%;
  filter: blur(15px);
  z-index: 3;
}

/* 添加粒子流效果连接地球和内容 */
.particle-stream {
  position: absolute;
  left: 30%;
  top: 0;
  bottom: 0;
  width: 100px;
  z-index: 3;
  pointer-events: none;
  overflow: hidden;
}

.particle-dot {
  position: absolute;
  width: 2px;
  height: 2px;
  border-radius: 50%;
  background: rgba(147, 112, 219, 0.8);
  filter: blur(1px);
  opacity: 0;
  animation: particleFlow 3s linear infinite;
}

@keyframes particleFlow {
  0% {
    transform: translate(0, -100%) scale(0.3);
    opacity: 0;
  }
  30% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.5;
  }
  100% {
    transform: translate(100px, 100%) scale(1.2);
    opacity: 0;
  }
}

@keyframes particleFlowVertical {
  0% {
    transform: translate(-100%, 0) scale(0.3);
    opacity: 0;
  }
  30% {
    opacity: 0.8;
  }
  80% {
    opacity: 0.5;
  }
  100% {
    transform: translate(100%, 30px) scale(1.2);
    opacity: 0;
  }
}

/* 移动端媒体查询调整 */
@media screen and (max-width: 992px) {
  .data-section-inner {
    background: radial-gradient(circle at 50% 30%, rgba(16, 20, 36, 0.3), transparent 70%);
  }

  .data-content {
    margin-left: 0;
    margin-top: -50px; /* 向上移动，与地球部分重叠 */
  }

  .globe-overlay-gradient {
    top: auto;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 150px;
    background: linear-gradient(0deg,
      rgba(16, 20, 36, 0.5) 0%,
      rgba(88, 101, 242, 0.2) 40%,
      rgba(16, 20, 36, 0) 100%
    );
  }

  .particle-stream {
    left: 0;
    right: 0;
    top: auto;
    bottom: 30%;
    width: 100%;
    height: 100px;
  }

  .particle-dot {
    animation: particleFlowVertical 3s linear infinite;
  }
}

@media (max-width: 768px) {
  .data-cta-button .starry-button,
  .explore-platform-btn {
    padding: 12px 28px;
    font-size: 0.95rem;
  }
}

/* 全新设计的工业园区定位模块 */
.futuristic-industrial-module {
  position: relative;
  width: 100%;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(10, 15, 30, 0.95) 0%, rgba(5, 10, 25, 0.97) 100%);
  overflow: hidden;
  z-index: 20;
  scroll-snap-align: start;
}

/* 微网格背景 */
.industrial-grid-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(30, 120, 255, 0.07) 1px, transparent 1px),
    linear-gradient(90deg, rgba(30, 120, 255, 0.07) 1px, transparent 1px);
  background-size: 30px 30px;
  opacity: 0.4;
  z-index: 1;
  pointer-events: none;
}

/* 动态电路图案 */
.circuit-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='none' stroke='rgba(0, 150, 255, 0.15)' stroke-width='1' d='M10,30 L30,30 L30,10 M70,10 L70,30 L90,30 M90,70 L70,70 L70,90 M30,90 L30,70 L10,70 M30,50 L70,50 M50,30 L50,70'/%3E%3C/svg%3E");
  background-size: 100px 100px;
  opacity: 0.3;
  z-index: 1;
  pointer-events: none;
}

/* 容器布局 */
.industrial-module-container {
  position: relative;
  display: flex;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  z-index: 5;
  padding: 0 30px;
}

/* 地球容器 - 保留原有地球效果，但调整位置和大小 */
.holographic-earth-container {
  position: relative;
  width: 45%;
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

/* 地球扫描环绕效果 */
.earth-scan-ring {
  position: absolute;
  border-radius: 50%;
  border: 1px solid rgba(0, 190, 255, 0.3);
  box-shadow: 0 0 20px rgba(0, 140, 255, 0.2);
  opacity: 0;
  animation: scanRingPulse 4s infinite ease-out;
}

.earth-scan-ring:nth-child(1) {
  width: 500px;
  height: 500px;
  animation-delay: 0s;
}

.earth-scan-ring:nth-child(2) {
  width: 540px;
  height: 540px;
  animation-delay: 1s;
}

.earth-scan-ring:nth-child(3) {
  width: 580px;
  height: 580px;
  animation-delay: 2s;
}

@keyframes scanRingPulse {
  0% {
    transform: scale(0.8);
    opacity: 0;
  }
  20% {
    opacity: 0.5;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

/* 地球连接点 */
.earth-connection-point {
  position: absolute;
  width: 6px;
  height: 6px;
  background: #00e5ff;
  border-radius: 50%;
  box-shadow: 0 0 10px #00a5ff, 0 0 20px rgba(0, 150, 255, 0.4);
  z-index: 15;
}

.connection-point-1 {
  top: 35%;
  right: 25%;
  animation: connectionPulse 3s infinite ease-in-out;
}

.connection-point-2 {
  top: 55%;
  right: 30%;
  animation: connectionPulse 3s infinite ease-in-out 0.5s;
}

.connection-point-3 {
  top: 40%;
  right: 40%;
  animation: connectionPulse 3s infinite ease-in-out 1s;
}

.connection-point-4 {
  top: 60%;
  right: 22%;
  animation: connectionPulse 3s infinite ease-in-out 1.5s;
}

@keyframes connectionPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.5;
  }
}

/* 连接线 */
.connection-line {
  position: absolute;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 200, 255, 0), rgba(0, 200, 255, 0.8), rgba(0, 200, 255, 0));
  transform-origin: left center;
  z-index: 12;
  opacity: 0;
  animation: lineConnect 3s infinite ease-in-out;
}

.connection-line-1 {
  top: 35%;
  right: 5%;
  width: 20%;
  transform: rotate(-10deg);
  animation-delay: 0.2s;
}

.connection-line-2 {
  top: 55%;
  right: 5%;
  width: 25%;
  transform: rotate(5deg);
  animation-delay: 0.7s;
}

.connection-line-3 {
  top: 40%;
  right: 0;
  width: 20%;
  transform: rotate(-5deg);
  animation-delay: 1.2s;
}

.connection-line-4 {
  top: 60%;
  right: 0;
  width: 22%;
  transform: rotate(10deg);
  animation-delay: 1.7s;
}

@keyframes lineConnect {
  0% {
    width: 0;
    opacity: 0;
  }
  20% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 0;
  }
}

/* 数据控制中心 */
.command-center-container {
  position: relative;
  width: 55%;
  padding-left: 60px;
  z-index: 15;
}

/* 模块外框 */
.command-center-frame {
  position: relative;
  width: 100%;
  background: rgba(15, 20, 35, 0.7);
  backdrop-filter: blur(8px);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.3);
  padding: 40px;
  border: 1px solid rgba(100, 180, 255, 0.2);
}

/* 顶部边框光效 */
.command-center-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg,
    rgba(0, 150, 255, 0.1),
    rgba(0, 220, 255, 0.8) 20%,
    rgba(60, 180, 255, 0.8) 50%,
    rgba(0, 220, 255, 0.8) 80%,
    rgba(0, 150, 255, 0.1)
  );
  z-index: 1;
}

/* 角落点缀 */
.corner-accent {
  position: absolute;
  width: 20px;
  height: 20px;
  border-style: solid;
  border-color: rgba(0, 180, 255, 0.6);
  z-index: 2;
}

.top-left-corner {
  top: 0;
  left: 0;
  border-width: 2px 0 0 2px;
  border-radius: 4px 0 0 0;
}

.top-right-corner {
  top: 0;
  right: 0;
  border-width: 2px 2px 0 0;
  border-radius: 0 4px 0 0;
}

.bottom-left-corner {
  bottom: 0;
  left: 0;
  border-width: 0 0 2px 2px;
  border-radius: 0 0 0 4px;
}

.bottom-right-corner {
  bottom: 0;
  right: 0;
  border-width: 0 2px 2px 0;
  border-radius: 0 0 4px 0;
}

/* 内容布局 */
.command-center-content {
  position: relative;
  z-index: 5;
}

/* 标题样式 */
.industrial-title-container {
  margin-bottom: 30px;
}

.industrial-title {
  margin: 0;
  padding: 0;
  font-family: 'Orbitron', sans-serif;
  text-transform: uppercase;
  line-height: 1.2;
  color: #ffffff;
  margin-bottom: 15px;
  position: relative;
  display: flex;
  flex-direction: column;
}

.industrial-title-line {
  display: block;
  font-size: 2.8rem;
  font-weight: 600;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  height: 3.4rem;
  opacity: 0;
  transform: translateY(20px);
  animation: titleLineReveal 0.8s forwards cubic-bezier(0.215, 0.61, 0.355, 1);
}

.industrial-title-line:nth-child(1) { animation-delay: 0.1s; }
.industrial-title-line:nth-child(2) { animation-delay: 0.3s; }
.industrial-title-line:nth-child(3) { animation-delay: 0.5s; }

.industrial-title-line::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg,
    rgba(0, 150, 255, 0),
    rgba(0, 220, 255, 0.6) 30%,
    rgba(0, 220, 255, 0.6) 70%,
    rgba(0, 150, 255, 0)
  );
  transform: scaleX(0);
  transform-origin: left center;
  animation: titleUnderlineGrow 1.2s forwards ease-out;
  animation-delay: 0.8s;
}

.industrial-title-word {
  display: inline-block;
  position: relative;
}

.industrial-title-highlight {
  color: #00e5ff;
  text-shadow: 0 0 10px rgba(0, 220, 255, 0.6);
  animation: titleHighlightGlow 3s infinite alternate;
}

@keyframes titleLineReveal {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes titleUnderlineGrow {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}

@keyframes titleHighlightGlow {
  0% {
    text-shadow: 0 0 10px rgba(0, 220, 255, 0.6);
  }
  100% {
    text-shadow: 0 0 20px rgba(0, 220, 255, 0.9), 0 0 30px rgba(0, 170, 255, 0.6);
  }
}

/* 描述样式 */
.industrial-description {
  font-family: 'Rajdhani', sans-serif;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.2rem;
  line-height: 1.6;
  margin-bottom: 25px;
  position: relative;
  opacity: 0;
  transform: translateY(15px);
  animation: descriptionFadeIn 0.8s forwards ease-out 1s;
}

.industrial-sub-description {
  font-family: 'Rajdhani', sans-serif;
  color: rgba(220, 230, 255, 0.7);
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 30px;
  opacity: 0;
  transform: translateY(15px);
  animation: descriptionFadeIn 0.8s forwards ease-out 1.2s;
}

@keyframes descriptionFadeIn {
  0% {
    opacity: 0;
    transform: translateY(15px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮样式增强 */
.command-center-cta {
  margin-top: 40px;
  opacity: 0;
  transform: translateY(15px);
  animation: descriptionFadeIn 0.8s forwards ease-out 1.4s;
}

.industrial-cta-button {
  position: relative;
  background: linear-gradient(45deg, rgba(0, 80, 200, 0.8), rgba(0, 150, 255, 0.8));
  color: white;
  font-family: 'Orbitron', sans-serif;
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 2px;
  text-transform: uppercase;
  padding: 16px 36px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 100, 255, 0.3);
  z-index: 1;
}

.industrial-cta-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
  z-index: -1;
}

.industrial-cta-button:hover {
  background: linear-gradient(45deg, rgba(0, 100, 220, 0.9), rgba(0, 180, 255, 0.9));
  box-shadow: 0 7px 20px rgba(0, 120, 255, 0.4);
  transform: translateY(-2px);
}

.industrial-cta-button:hover::before {
  left: 100%;
}

.industrial-cta-button:active {
  transform: translateY(1px);
  box-shadow: 0 3px 10px rgba(0, 100, 255, 0.3);
}

/* 数据节点图标 */
.data-nodes {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
}

.data-node {
  position: absolute;
  width: 8px;
  height: 8px;
  background: rgba(0, 150, 255, 0.7);
  border-radius: 50%;
  transform: scale(0);
  animation: nodeAppear 3s infinite ease-out;
  box-shadow: 0 0 10px rgba(0, 200, 255, 0.8);
}

.node-1 {
  top: 20%;
  right: 10%;
  animation-delay: 0.5s;
}

.node-2 {
  bottom: 30%;
  right: 15%;
  animation-delay: 1.2s;
}

.node-3 {
  top: 40%;
  left: 10%;
  animation-delay: 1.8s;
}

.node-4 {
  bottom: 20%;
  left: 15%;
  animation-delay: 2.5s;
}

@keyframes nodeAppear {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  20% {
    transform: scale(1.5);
    opacity: 1;
  }
  40% {
    transform: scale(1);
    opacity: 0.8;
  }
  100% {
    transform: scale(0);
    opacity: 0;
  }
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
  .industrial-module-container {
    padding: 0 20px;
  }

  .holographic-earth-container {
    width: 40%;
  }

  .command-center-container {
    width: 60%;
    padding-left: 30px;
  }

  .industrial-title-line {
    font-size: 2.4rem;
    height: 3rem;
  }
}

@media screen and (max-width: 992px) {
  .industrial-module-container {
    flex-direction: column;
    align-items: center;
    padding: 60px 20px;
  }

  .holographic-earth-container {
    width: 100%;
    height: 400px;
    margin-bottom: 40px;
  }

  .command-center-container {
    width: 100%;
    padding-left: 0;
  }

  .command-center-frame {
    padding: 30px;
  }

  .industrial-title-line {
    font-size: 2.2rem;
    height: 2.8rem;
    text-align: center;
  }

  .industrial-description,
  .industrial-sub-description {
    text-align: center;
  }

  .command-center-cta {
    display: flex;
    justify-content: center;
  }

  .earth-connection-point,
  .connection-line {
    display: none;
  }
}

@media screen and (max-width: 576px) {
  .industrial-module-container {
    padding: 40px 15px;
  }

  .holographic-earth-container {
    height: 300px;
  }

  .command-center-frame {
    padding: 20px;
  }

  .industrial-title-line {
    font-size: 1.8rem;
    height: 2.3rem;
  }

  .industrial-description {
    font-size: 1.1rem;
  }

  .industrial-sub-description {
    font-size: 0.9rem;
  }

  .industrial-cta-button {
    padding: 14px 28px;
    font-size: 1rem;
  }
}

/* 新增：现代科技感地球数据模块样式 */
.earth-data-section {
  position: relative;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1a2e 50%, #16213e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  margin: 0;
  padding: 0;
}

.global-map-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  overflow: hidden;
  background: transparent;
  border: none;
  margin: 0;
  padding: 0;
}

.global-map-container canvas {
  display: block;
  width: 100% !important;
  height: 100% !important;
}

@media (max-width: 768px) {
  .global-map-container {
    height: 100vh;
    width: 100vw;
  }
}

/* 简洁页脚样式 */
.site-footer {
  background: linear-gradient(135deg, #0a0f1c 0%, #1a1a2e 100%);
  border-top: 1px solid rgba(77, 200, 255, 0.2);
  padding: 40px 0 20px;
  position: relative;
  overflow: hidden;
}

.site-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(77, 200, 255, 0.5), 
    transparent
  );
  animation: footerGlow 3s ease-in-out infinite;
}

@keyframes footerGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 1; }
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.footer-brand {
  flex: 1;
}

.footer-logo {
  font-family: 'Orbitron', sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  letter-spacing: 2px;
}

.logo-highlight {
  color: #4dc8ff;
  text-shadow: 0 0 10px rgba(77, 200, 255, 0.5);
}

.logo-normal {
  color: #ffffff;
  margin-left: 8px;
}

.footer-tagline {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
  font-weight: 300;
}

.footer-contact {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.contact-email,
.contact-location {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-email:hover {
  color: #4dc8ff;
  cursor: pointer;
}

.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  margin: 0;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.6);
  text-decoration: none;
  font-size: 0.85rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #4dc8ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-footer {
    padding: 30px 0 15px;
  }
  
  .footer-container {
    padding: 0 20px;
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .footer-contact {
    align-items: center;
  }
  
  .footer-bottom {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .footer-links {
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .footer-logo {
    font-size: 1.3rem;
  }
  
  .footer-links {
    flex-direction: column;
    gap: 10px;
  }
}