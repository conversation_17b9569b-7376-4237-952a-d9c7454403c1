{"name": "industrialgeodev", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@react-three/postprocessing": "^3.0.4", "@turf/turf": "^7.2.0", "animejs": "^3.2.2", "chart.js": "^3.9.1", "d3": "^7.9.0", "framer-motion": "^12.12.1", "gsap": "^3.13.0", "i18next": "^24.2.3", "leaflet": "^1.9.4", "leaflet-draw": "^1.0.4", "leaflet-geometryutil": "^0.10.3", "leaflet.markercluster": "^1.5.3", "mapbox-gl": "^3.10.0", "papaparse": "^5.5.2", "react": "^19.0.0", "react-chartjs-2": "^4.3.1", "react-dom": "^19.0.0", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-leaflet": "^5.0.0", "react-leaflet-markercluster": "^5.0.0-rc.0", "react-modal": "^3.16.3", "react-router-dom": "^7.5.3", "recharts": "^2.15.1", "three": "^0.176.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@stagewise/toolbar-react": "^0.1.2", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.0", "autoprefixer": "^10.4.21", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "tailwindcss": "^4.1.7", "vite": "^6.2.0"}}